# Simple Vercel AI SDK + Braintrust Examples

This directory contains the simplest possible examples of how to integrate Braintrust logging with the Vercel AI SDK.

## Prerequisites

1. **Install dependencies:**
   ```bash
   # For OpenAI examples
   npm install @ai-sdk/openai ai braintrust

   # For Google Vertex AI examples (Claude models)
   npm install @ai-sdk/google-vertex ai braintrust
   ```

2. **Set environment variables:**

   **For OpenAI:**
   ```bash
   export BRAINTRUST_API_KEY="your-braintrust-api-key"
   export OPENAI_API_KEY="your-openai-api-key"
   ```

   **For Google Vertex AI (Claude models):**
   ```bash
   export BRAINTRUST_API_KEY="your-braintrust-api-key"
   export GOOGLE_CLOUD_PROJECT_ID="your-gcp-project-id"
   export GOOGLE_CLOUD_LOCATION="us-central1"  # Optional, defaults to us-central1
   export GOOGLE_APPLICATION_CREDENTIALS="path/to/service-account-key.json"
   ```

## Examples

### Example 1: AI SDK v4 Style (`simple-vercel-ai-example.js`)

Uses the `wrapAISDKModel()` function - the traditional approach:

```javascript
import { initLogger, wrapAISDKModel } from "braintrust";

const logger = initLogger({
  projectName: "my-simple-ai-project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const model = wrapAISDKModel(openai("gpt-3.5-turbo"));
```

**Run it:**
```bash
node simple-vercel-ai-example.js
```

### Example 2: AI SDK v5 Style (`simple-vercel-ai-v5-example.js`)

Uses the newer `BraintrustMiddleware()` approach:

```javascript
import { initLogger, BraintrustMiddleware } from "braintrust";

initLogger({
  projectName: "my-simple-ai-project-v5",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const model = wrapLanguageModel({
  model: openai("gpt-3.5-turbo"),
  middleware: BraintrustMiddleware({ debug: true }),
});
```

**Run it:**
```bash
node simple-vercel-ai-v5-example.js
```

### Example 3: Google Vertex AI Claude (`vertex-ai-claude-example.js`)

Uses Claude Opus 4.1 from Google Cloud Vertex AI:

```javascript
import { vertex } from "@ai-sdk/google-vertex";
import { initLogger, BraintrustMiddleware } from "braintrust";

const model = wrapLanguageModel({
  model: vertex("claude-opus-4-1-20250805", {
    project: process.env.GOOGLE_CLOUD_PROJECT_ID,
    location: process.env.GOOGLE_CLOUD_LOCATION || "us-central1",
  }),
  middleware: BraintrustMiddleware({ debug: true }),
});
```

**Run it:**
```bash
node vertex-ai-claude-example.js
```

## What Gets Logged

Both examples will automatically log to Braintrust:
- ✅ Input prompt
- ✅ Model response
- ✅ Token usage
- ✅ Timing metrics
- ✅ Model metadata

## Google Cloud Setup for Vertex AI

To use Claude models through Vertex AI, you need:

1. **Enable Vertex AI API** in your Google Cloud project
2. **Set up authentication** using one of these methods:
   - Service Account Key: Download JSON key and set `GOOGLE_APPLICATION_CREDENTIALS`
   - Application Default Credentials: Run `gcloud auth application-default login`
   - Workload Identity (for GKE/Cloud Run)

3. **Enable Claude models** in Vertex AI Model Garden (if not already enabled)

## Next Steps

After running any example:
1. Go to your Braintrust dashboard
2. Find your project ("my-simple-ai-project", "my-simple-ai-project-v5", or "vertex-ai-claude-example")
3. View the logged interactions with full details

That's it! The integration handles all the logging automatically.
