# Simple Vercel AI SDK + Braintrust Examples

This directory contains the simplest possible examples of how to integrate Braintrust logging with the Vercel AI SDK.

## Prerequisites

1. **Install dependencies:**
   ```bash
   npm install @ai-sdk/openai ai braintrust
   ```

2. **Set environment variables:**
   ```bash
   export BRAINTRUST_API_KEY="your-braintrust-api-key"
   export OPENAI_API_KEY="your-openai-api-key"
   ```

## Examples

### Example 1: AI SDK v4 Style (`simple-vercel-ai-example.js`)

Uses the `wrapAISDKModel()` function - the traditional approach:

```javascript
import { initLogger, wrapAISDKModel } from "braintrust";

const logger = initLogger({
  projectName: "my-simple-ai-project",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const model = wrapAISDKModel(openai("gpt-3.5-turbo"));
```

**Run it:**
```bash
node simple-vercel-ai-example.js
```

### Example 2: AI SDK v5 Style (`simple-vercel-ai-v5-example.js`)

Uses the newer `BraintrustMiddleware()` approach:

```javascript
import { initLogger, BraintrustMiddleware } from "braintrust";

initLogger({
  projectName: "my-simple-ai-project-v5",
  apiKey: process.env.BRAINTRUST_API_KEY,
});

const model = wrapLanguageModel({
  model: openai("gpt-3.5-turbo"),
  middleware: BraintrustMiddleware({ debug: true }),
});
```

**Run it:**
```bash
node simple-vercel-ai-v5-example.js
```

## What Gets Logged

Both examples will automatically log to Braintrust:
- ✅ Input prompt
- ✅ Model response
- ✅ Token usage
- ✅ Timing metrics
- ✅ Model metadata

## Next Steps

After running either example:
1. Go to your Braintrust dashboard
2. Find your project ("my-simple-ai-project" or "my-simple-ai-project-v5")
3. View the logged interactions with full details

That's it! The integration handles all the logging automatically.
