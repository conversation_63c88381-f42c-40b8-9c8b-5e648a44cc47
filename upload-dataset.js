import { initDataset } from "braintrust";

// Configuration - replace these with your actual values
const BRAINTRUST_PROJECT_NAME = "Your Project Name"; // Replace with your project name
const datasetName = "Your Dataset Name"; // Replace with your dataset name

// Sample dataset items - replace with your actual data
const datasetItems = [
  {
    input: "What is the capital of France?",
    expected: "Paris",
    metadata: { category: "geography", difficulty: "easy" }
  },
  {
    input: "What is 2 + 2?",
    expected: "4",
    metadata: { category: "math", difficulty: "easy" }
  },
  {
    input: "Explain quantum computing",
    expected: "Quantum computing is a type of computation that harnesses quantum mechanical phenomena...",
    metadata: { category: "science", difficulty: "hard" }
  }
];

// Simple logging function
function log(message) {
  console.log(message);
}

async function uploadDataset() {
  try {
    // Initialize the dataset
    const dataset = initDataset({
      project: BRAINTRUST_PROJECT_NAME,
      dataset: datasetName,
    });

    log(`\nUploading dataset to Braintrust project: ${BRAINTRUST_PROJECT_NAME}`);
    log(`Dataset name: ${datasetName}`);
    log(`Number of items to upload: ${datasetItems.length}`);

    // Insert each item into the dataset
    for (const item of datasetItems) {
      const id = dataset.insert(item);
      log(`Inserted record with id: ${id}`);
    }

    log("\nFlushing dataset...");
    await dataset.flush();
    
    log("Dataset upload completed successfully!");
    
    // Optional: Display dataset summary
    const summary = await dataset.summarize();
    log("\nDataset Summary:");
    log(`Total records: ${summary.dataSummary?.totalRecords || 'N/A'}`);
    
  } catch (error) {
    console.error("Error uploading dataset:", error);
    process.exit(1);
  }
}

// Run the upload
uploadDataset().catch(console.error);
