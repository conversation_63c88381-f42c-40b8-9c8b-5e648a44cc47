#!/usr/bin/env node

// Using createVertexAnthropic for Claude models on Vertex AI
import { createVertexAnthropic } from "@ai-sdk/google-vertex/anthropic";
import { generateText, wrapLanguageModel } from "ai";
import { initLogger, BraintrustMiddleware } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  initLogger({
    projectName: "pedro-project1",
    apiKey: process.env.BRAINTRUST_API_KEY,
  });

  // 2. Create Vertex Anthropic provider
  const vertexAnthropic = createVertexAnthropic({
    project: process.env.GOOGLE_CLOUD_PROJECT_ID,
    location: process.env.GOOGLE_VERTEX_LOCATION || "us-east5",
  });

  // 3. Create Claude model and wrap with Braintrust middleware
  const model = wrapLanguageModel({
    // Available models: claude-3-5-sonnet-v2@20241022, claude-3-5-ha<PERSON>u@20241022, claude-3-opus@20240229
    model: vertexAnthropic("claude-3-5-sonnet-v2@20241022"),
    middleware: BraintrustMiddleware({ debug: true, name: "VertexAnthropic" }),
  });

  // 4. Use Claude via Vertex AI - automatically logged to Braintrust
  const result = await generateText({
    model: model,
    prompt: "Explain the benefits of using Vertex AI for Claude models",
    maxTokens: 500,
  });

  console.log("Claude Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
