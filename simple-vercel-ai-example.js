#!/usr/bin/env node

// Simple example of Vercel AI SDK integration with Braintrust
import { openai } from "@ai-sdk/openai";
import { generateText } from "ai";
import { initLogger, wrapAISDKModel } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  const logger = initLogger({
    projectName: "my-simple-ai-project",
    apiKey: process.env.BRAINTRUST_API_KEY, // Set this in your environment
  });

  // 2. Wrap your AI SDK model with Braintrust tracing
  const model = wrapAISDKModel(openai("gpt-3.5-turbo"));

  // 3. Use the model - this will automatically log to Braintrust
  const result = await generateText({
    model: model,
    prompt: "What is the capital of France?",
  });

  console.log("Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
