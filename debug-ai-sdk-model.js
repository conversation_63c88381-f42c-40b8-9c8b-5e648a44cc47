#!/usr/bin/env node

// Debug script to understand your AI SDK model structure
import { openai } from "@ai-sdk/openai";

async function main() {
  console.log("🔍 Debugging AI SDK Model Structure\n");
  
  const model = openai("gpt-3.5-turbo");
  
  console.log("Model object:", model);
  console.log("\nModel properties:");
  console.log("- specificationVersion:", model?.specificationVersion);
  console.log("- provider:", model?.provider);
  console.log("- modelId:", model?.modelId);
  console.log("- constructor name:", model?.constructor?.name);
  
  console.log("\nAll enumerable properties:");
  for (const key in model) {
    console.log(`- ${key}:`, typeof model[key], model[key]);
  }
  
  console.log("\nObject.getOwnPropertyNames:");
  try {
    const props = Object.getOwnPropertyNames(model);
    props.forEach(prop => {
      console.log(`- ${prop}:`, typeof model[prop]);
    });
  } catch (e) {
    console.log("Could not get property names:", e.message);
  }
  
  // Check if this would work with wrapAISDKModel
  const isV1Compatible = 
    model?.specificationVersion === "v1" &&
    typeof model?.provider === "string" &&
    typeof model?.modelId === "string";
    
  console.log(`\n✅ Compatible with wrapAISDKModel: ${isV1Compatible}`);
  
  if (!isV1Compatible) {
    console.log("❌ This model is NOT compatible with wrapAISDKModel()");
    console.log("✅ Use BraintrustMiddleware with wrapLanguageModel() instead");
  }
}

main().catch(console.error);
