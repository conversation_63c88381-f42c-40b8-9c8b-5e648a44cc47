#!/usr/bin/env node

// Example using Claude Opus 4.1 from Google Cloud Vertex AI with Braintrust
import { vertex } from "@ai-sdk/google-vertex";
import { generateText, wrapLanguageModel } from "ai";
import { initLogger, BraintrustMiddleware } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  initLogger({
    projectName: "vertex-ai-claude-example",
    apiKey: process.env.BRAINTRUST_API_KEY,
  });

  // 2. Create Vertex AI client for Claude Opus 4.1
  const model = wrapLanguageModel({
    model: vertex("claude-opus-4-1-20250805", {
      project: process.env.GOOGLE_CLOUD_PROJECT_ID,
      location: process.env.GOOGLE_CLOUD_LOCATION || "us-central1",
    }),
    middleware: BraintrustMiddleware({ debug: true, name: "VertexClaude" }),
  });

  // 3. Use Claude Opus 4.1 - automatically logged to Braintrust
  const result = await generateText({
    model: model,
    prompt: "Explain the concept of recursion in programming with a simple example.",
    maxTokens: 500,
  });

  console.log("<PERSON> 4.1 Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
