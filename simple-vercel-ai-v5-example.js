#!/usr/bin/env node

// Simple example using AI SDK v5 middleware approach with Braintrust
// Using Claude Opus 4.1 from Google Cloud Vertex AI
import { vertex } from "@ai-sdk/google-vertex";
import { generateText, wrapLanguageModel } from "ai";
import { initLogger, BraintrustMiddleware } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  initLogger({
    projectName: "my-simple-ai-project-v5-vertex",
    apiKey: process.env.BRAINTRUST_API_KEY, // Set this in your environment
  });

  // 2. Create Vertex AI client and wrap with Braintrust middleware
  const model = wrapLanguageModel({
    model: vertex("claude-opus-4-1-20250805", {
      // Your Google Cloud project ID
      project: process.env.GOOGLE_CLOUD_PROJECT_ID,
      // Your Google Cloud region (optional, defaults to us-central1)
      location: process.env.GOOGLE_CLOUD_LOCATION || "us-central1",
    }),
    middleware: BraintrustMiddleware({ debug: true }),
  });

  // 3. Use the model - this will automatically log to Braintrust
  const result = await generateText({
    model: model,
    prompt: "Write a haiku about coding with <PERSON> <PERSON> 4.1",
  });

  console.log("Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
