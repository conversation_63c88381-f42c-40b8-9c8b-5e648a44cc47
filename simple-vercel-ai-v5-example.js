#!/usr/bin/env node

// Simple example using AI SDK v5 middleware approach with Braintrust
import { openai } from "@ai-sdk/openai";
import { generateText, wrapLanguageModel } from "ai";
import { initLogger, BraintrustMiddleware } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  initLogger({
    projectName: "my-simple-ai-project-v5",
    apiKey: process.env.BRAINTRUST_API_KEY, // Set this in your environment
  });

  // 2. Wrap your model with Braintrust middleware
  const model = wrapLanguageModel({
    model: openai("gpt-3.5-turbo"),
    middleware: BraintrustMiddleware({ debug: true }),
  });

  // 3. Use the model - this will automatically log to Braintrust
  const result = await generateText({
    model: model,
    prompt: "Write a haiku about coding",
  });

  console.log("Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
