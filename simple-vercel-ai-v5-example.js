#!/usr/bin/env node

// Simple example using AI SDK v5 middleware approach with Braintrust
// Using Claude models from Google Cloud Vertex AI with createVertexAnthropic
import { createVertexAnthropic } from "@ai-sdk/google-vertex/anthropic";
import { generateText, wrapLanguageModel } from "ai";
import { initLogger, BraintrustMiddleware } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  initLogger({
    projectName: "pedro-project1",
    apiKey: process.env.BRAINTRUST_API_KEY,
  });

  // 2. Create Vertex Anthropic provider
  const vertexAnthropic = createVertexAnthropic({
    project: process.env.GOOGLE_CLOUD_PROJECT_ID,
    location: process.env.GOOGLE_VERTEX_LOCATION || "us-east5",
  });

  // 3. Create Claude model and wrap with Braintrust middleware
  const model = wrapLanguageModel({
    model: vertexAnthropic("claude-3-5-sonnet-v2@20241022"),
    middleware: BraintrustMiddleware({ debug: true }),
  });

  // 4. Use Claude via Vertex AI - this will automatically log to Braintrust
  const result = await generateText({
    model: model,
    prompt: "Write a haiku about coding with Claude via Vertex AI",
  });

  console.log("Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
