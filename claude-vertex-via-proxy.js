#!/usr/bin/env node

// Using Claude Opus 4.1 from Vertex AI via Braintrust Proxy
// This is the recommended approach for Claude models on Vertex AI
import { openai } from "@ai-sdk/openai";
import { generateText, wrapLanguageModel } from "ai";
import { initLogger, BraintrustMiddleware } from "braintrust";

async function main() {
  // 1. Initialize Braintrust logger
  initLogger({
    projectName: "pedro-project1",
    apiKey: process.env.BRAINTRUST_API_KEY,
  });

  // 2. Create Claude model via Braintrust proxy (supports Vertex AI)
  const model = wrapLanguageModel({
    model: openai("claude-opus-4-1-20250805", {
      baseURL: "https://api.braintrust.dev/v1/proxy",
      apiKey: process.env.BRAINTRUST_API_KEY,
    }),
    middleware: BraintrustMiddleware({ debug: true }),
  });

  // 3. Use Claude Opus 4.1 - this will automatically log to Braintrust
  const result = await generateText({
    model: model,
    prompt: "Write a haiku about coding with Claude Opus 4.1",
  });

  console.log("Response:", result.text);
  console.log("✅ Check your Braintrust project to see the logged interaction!");
}

main().catch(console.error);
